===============================================
橙設物業管理系統 - 版本修改紀錄
===============================================

版本：v2.1.3
編譯日期：2025-07-24
編譯 Hash：3c615cd4835da918
編譯時間：90083ms (約 90 秒)

===============================================
主要修正項目
===============================================

1. 【圖示顯示修正】
   問題：首頁儀表板中多個圖示顯示為空白
   原因：vue-unicons 圖示名稱錯誤或不存在
   修正：
   - arrows-alt → expand-arrows (拖曳排序按鈕)
   - expand → expand-arrows (展開按鈕)
   - trash → trash-alt (刪除按鈕)
   - setting → cog (設定按鈕)
   - maximize → corner-up-right (調整大小按鈕)
   影響檔案：src/view/oco/home/<USER>

2. 【編輯模式開關功能】
   問題：編輯按鈕與設定/刪除按鈕重疊，介面混亂
   解決方案：新增編輯模式開關控制編輯按鈕顯示
   功能：
   - 新增編輯模式開關 (位於「新增圖表」按鈕旁)
   - 控制三個編輯按鈕的顯示/隱藏：
     * 拖曳排序按鈕 (expand-arrows)
     * 調整大小按鈕 (corner-up-right)
     * 恢復預設大小按鈕 (refresh)
   - 預設狀態：編輯模式關閉
   - 避免按鈕重疊，提供清晰的編輯/檢視模式切換
   影響檔案：
   - src/view/oco/home/<USER>
   - src/view/oco/home/<USER>

3. 【用戶名稱顯示修正】
   問題：第一次登入顯示預設用戶名，登出再登入後才顯示正確名稱
   原因：組件靜態讀取 localStorage，不響應後續變化
   修正：
   - 將靜態讀取改為響應式 ref 和 computed
   - 新增 updateStaffName() 函數動態更新用戶名稱
   - 監聽 storage 事件，localStorage 變化時自動更新
   - 登入成功後手動觸發 storage 事件通知
   - 組件生命週期管理，避免記憶體洩漏
   影響檔案：
   - src/components/utilities/auth-info/info.vue
   - src/vuex/modules/auth/actionCreator.js

4. 【SignalR 連接修正】
   問題：CCTV 和 Alarm 的 SignalR WebSocket 連接失敗
   原因：前端端口與後端 CORS 設定不匹配
   修正：
   - 前端開發端口從 8999 改為 8080
   - 更新環境變數 VUE_APP_FRONTEND_URL
   - 修正 nginx CORS 設定匹配新端口
   - 統一 AlarmSummary 和 Cctv 使用 /api 路徑
   影響檔案：
   - customize-vue-config.js
   - .env
   - nginx.conf
   - src/composable/alarmConnection.js

5. 【圖示服務代理修正】
   問題：生產環境中圖示無法載入
   原因：nginx 缺少 /imgApi/ 代理設定
   修正：
   - 新增 nginx /imgApi/ 代理設定
   - 代理到圖示服務器 (http://*************:7654/)
   - 確保生產環境圖示正確載入
   影響檔案：nginx.conf

===============================================
技術細節
===============================================

前端設定：
- 開發端口：8080 (原 8999)
- 生產端口：8345 (nginx 代理)
- API 端點：http://*************:8345/
- 圖示服務：http://*************:7654/

後端相容性：
- CORS 設定：http://localhost:8080
- SignalR Hub：/AlarmSummary, /Cctv
- API 路徑：/api/*

===============================================
測試驗證
===============================================

✅ 圖示顯示：所有按鈕圖示正確顯示
✅ 編輯模式：開關正常控制編輯按鈕顯示/隱藏
✅ 用戶名稱：登入後立即顯示正確用戶名稱
✅ SignalR：CCTV 和 Alarm 連接正常 (需後端支援)
✅ 圖示載入：生產環境圖示正確載入
✅ 編譯成功：無錯誤，所有功能正常

===============================================
部署說明
===============================================

1. 前端編譯：npm run build
2. 部署 dist 目錄到生產環境
3. 確認 nginx 服務重新啟動
4. 驗證所有功能正常運作

===============================================
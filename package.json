{"name": "plc2.0", "version": "0.0.1", "private": true, "posthtml": {"recognizeSelfClosing": true}, "scripts": {"serve": "vue-cli-service serve", "build": "set \"GENERATE_SOURCEMAP=false\" && vue-cli-service --max-old-space-size=4096 build", "lint": "vue-cli-service lint", "start": "http-server dist", "test": "jest"}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@ckeditor/ckeditor5-build-classic": "^31.1.0", "@ckeditor/ckeditor5-vue": "^2.0.1", "@fortawesome/fontawesome-svg-core": "^1.2.34", "@fortawesome/free-brands-svg-icons": "^5.15.2", "@fortawesome/free-solid-svg-icons": "^5.15.2", "@fortawesome/vue-fontawesome": "^3.0.0-3", "@microsoft/signalr": "^8.0.0", "@popperjs/core": "^2.11.4", "@voerro/vue-tagsinput": "^2.4.3", "@vue-leaflet/vue-leaflet": "^0.6.1", "@vueup/vue-quill": "^1.2.0", "ant-design-vue": "^3.2.15", "apexcharts": "^3.33.2", "auth0-lock": "^11.32.2", "axios": "0.21.1", "chart.js": "^3.7.1", "chartjs-plugin-zoom": "^2.0.1", "core-js": "^3.27.1", "countdown-js": "^1.0.2", "dayjs": "1.11.9", "emoji-picker-element": "^1.4.0", "exceljs": "^4.4.0", "fabric": "^5.3.0", "feather-icons": "^4.29.0", "file-saver": "^2.0.5", "firebase": "8.4.1", "glightbox": "^3.2.0", "jest": "^29.7.0", "jest-worker": "^27.4.6", "js-cookie": "^3.0.1", "leaflet": "^1.7.1", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "modal-video": "^2.4.6", "mqtt": "^5.0.5", "node-polyfill-webpack-plugin": "^2.0.1", "qs": "^6.11.2", "svgmap": "^2.7.2", "v-calendar": "^3.0.0-alpha.8", "vue": "3.2.45", "vue-clipboard3": "^2.0.0", "vue-draggable-next": "^2.2.1", "vue-i18n": "9", "vue-inline-svg": "^3.0.0-beta.2", "vue-json-csv": "^1.2.12", "vue-masonry": "^0.14.0", "vue-router": "^4.2.1", "vue-types": "^4.1.1", "vue-unicons": "^3.3.1", "vue2-daterange-picker": "^0.6.3", "vue3-apexcharts": "^1.4.1", "vue3-autocounter": "^1.0.6", "vue3-googl-chart": "^0.0.1", "vue3-google-map": "^0.8.5", "vue3-perfect-scrollbar": "^1.6.0", "vue3-styled-components": "^1.2.1", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "xlsx": "^0.18.4"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.2.26", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "css-loader": "^6.7.3", "devextreme-themebuilder": "^23.1.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.2.0", "esm": "^3.2.25", "less": "^4.1.3", "less-loader": "^11.1.0", "node-sass": "^8.0.0", "sass": "^1.57.1", "sass-loader": "13.2.0", "swiper": "^8.4.5", "terser-webpack-plugin": "^5.3.11", "thread-loader": "^4.0.4"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"vue/no-use-v-if-with-v-for": ["error", {"allowUsingIterationVar": true}], "vue/multi-word-component-names": 0}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
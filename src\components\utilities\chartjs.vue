<template>
  <canvas :class="className" :id="id" :style="style" :height="height"></canvas>
</template>
<script>
import {
  Chart,
  ArcElement,
  LineElement,
  BarElement,
  PointElement,
  BarController,
  B<PERSON>ble<PERSON>ontroller,
  <PERSON><PERSON>ut<PERSON>ontroller,
  Line<PERSON><PERSON>roller,
  <PERSON><PERSON><PERSON>roller,
  PolarAreaController,
  RadarController,
  ScatterController,
  CategoryScale,
  LinearScale,
  LogarithmicScale,
  RadialLinearScale,
  TimeScale,
  TimeSeriesScale,
  Decimation,
  Filler,
  Legend,
  Title,
  Tooltip,
  SubTitle,
} from "chart.js";
import VueTypes from "vue-types";
import {
  defineComponent,
  onBeforeUnmount,
  onMounted,
  toRefs,
  nextTick,
  watch,
  ref,
} from "vue";
import { customTooltips } from "../utilities/utilities";
import zoomPlugin from "chartjs-plugin-zoom";

Chart.register(
  ArcElement,
  LineElement,
  BarElement,
  PointElement,
  BarC<PERSON>roller,
  B<PERSON>ble<PERSON><PERSON>roller,
  <PERSON><PERSON>ut<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roller,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Polar<PERSON>rea<PERSON><PERSON><PERSON><PERSON>,
  Radar<PERSON><PERSON>roll<PERSON>,
  <PERSON>atter<PERSON><PERSON><PERSON><PERSON>,
  CategoryScale,
  LinearScale,
  LogarithmicScale,
  RadialLinearScale,
  TimeScale,
  TimeSeriesScale,
  Decimation,
  Filler,
  Legend,
  Title,
  Tooltip,
  SubTitle,
  zoomPlugin
);

export default defineComponent({
  name: "ChartJs",
  props: {
    type: VueTypes.string.def("line"),
    className: VueTypes.string.isRequired.def("bar"),
    id: VueTypes.string.isRequired.def("bar"),
    style: VueTypes.object.def({ marginBottom: "20px" }),
    labels: VueTypes.arrayOf(VueTypes.string).def([
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ]),
    height: VueTypes.oneOfType([String, Number]).def(479),
    scales: VueTypes.object.def({
      y: {
        beginAtZero: true,
        grid: {
          color: "#485e9029",
          borderDash: [3, 3],
          zeroLineColor: "#485e9029",
          zeroLineWidth: 1,
        },
        ticks: {
          beginAtZero: true,
          fontSize: 14,
          fontFamily: "Jost",
          color: "#8C90A4",
          max: 80,
          stepStartValue: 5,
          stepSize: 20,
          padding: 10,
          callback(label) {
            return `${label}k`;
          },
        },
      },
      x: {
        grid: {
          display: false,
          drawBorder: false,
          zeroLineWidth: 0,
          color: "transparent",
          z: 1,
        },
        ticks: {
          beginAtZero: true,
          fontSize: 14,
          fontFamily: "Jost",
          color: "#8C90A4",
        },
      },
    }),
    datasets: VueTypes.arrayOf(VueTypes.object).def([
      {
        data: [20, 60, 50, 45, 50, 60, 70, 40, 45, 35, 25, 30],
        backgroundColor: "#001737",
        barPercentage: 0.6,
        label: "Profit",
      },
      {
        data: [10, 40, 30, 40, 60, 55, 45, 35, 30, 20, 15, 20],
        backgroundColor: "#1ce1ac",
        barPercentage: 0.6,
        label: "Lose",
      },
    ]),
    layout: VueTypes.object.def({}),
    legend: VueTypes.object.def({
      display: false,
      labels: {
        display: false,
        position: "center",
      },
    }),
    elements: VueTypes.object.def({
      line: {
        tension: 0.6,
        borderCapStyle: "round",
        borderJoinStyle: "round",
        capBezierPoints: true,
      },
      point: {
        radius: 0,
        z: 5,
      },
    }),
    options: VueTypes.object.def({}),
    tooltip: VueTypes.object.def({
      callbacks: {
        label(t) {
          const dstLabel = t.dataset.label;
          const { formattedValue } = t;
          return `${dstLabel}:  ${formattedValue} `;
        },
        labelColor(t) {
          return {
            backgroundColor: t.dataset.hoverBackgroundColor,
            borderColor: "transparent",
          };
        },
      },
    }),
  },
  setup(props) {
    const {
      type,
      datasets,
      options,
      labels,
      id,
      tooltip,
      scales,
      elements,
      legend,
      layout,
    } = toRefs(props);

    const chart = ref(null);
    const lastDataHash = ref('');
    const isInitialized = ref(false);

    // 計算資料雜湊值，用於比較資料是否真正變化
    const getDataHash = () => {
      return JSON.stringify({
        labels: labels.value,
        datasets: datasets.value?.map(d => ({
          data: d.data,
          label: d.label,
          backgroundColor: d.backgroundColor,
          borderColor: d.borderColor
        }))
      });
    };

    // 檢查資料是否有變化
    const hasDataChanged = () => {
      const currentHash = getDataHash();
      if (currentHash !== lastDataHash.value) {
        lastDataHash.value = currentHash;
        return true;
      }
      return false;
    };

    // 初始化圖表（只執行一次）
    const initChart = () => {
      if (isInitialized.value) return;

      nextTick(() => {
        const chartElement = document.getElementById(`${id.value}`);
        if (!chartElement || chart.value) return;

        chart.value = new Chart(chartElement, {
          type: type.value,
          data: {
            labels: labels.value,
            datasets: datasets.value,
          },
          options: {
            responsive: true,
            maintainAspectRatio: true,
            layout,
            hover: {
              mode: "index",
              intersect: false,
            },
            plugins: {
              legend: legend.value,
              tooltip: {
                yAlign: "bottom",
                mode: "index",
                intersect: false,
                backgroundColor: "#ffffff",
                boxShadow: "0 8px 5px #ADB5D915",
                position: "average",
                titleColor: "#ADB5D9",
                color: "#ADB5D9",
                titleFontSize: 12,
                titleSpacing: 10,
                bodyColor: "#404040",
                bodyFontSize: 11,
                bodyFontStyle: "normal",
                bodyFontFamily: "'Jost', sans-serif",
                borderColor: "#F1F2F6",
                usePointStyle: true,
                borderWidth: 1,
                bodySpacing: 10,
                padding: {
                  x: 10,
                  y: 8,
                },
                z: 999999,
                enabled: false,
                external: customTooltips,
                ...tooltip.value,
              },
            },
            elements: elements.value,
            scales: scales.value,
            ...options.value,
          },
        });

        isInitialized.value = true;
        lastDataHash.value = getDataHash();
      });
    };

    // 平滑更新圖表資料
    const updateChart = () => {
      if (!chart.value || !hasDataChanged()) return;

      try {
        // 更新資料
        chart.value.data.labels = labels.value;
        chart.value.data.datasets = datasets.value;

        // 使用 'none' 模式進行無動畫更新，避免閃爍
        chart.value.update('none');
      } catch (error) {
        console.warn('Chart update failed:', error);
        // 如果更新失敗，重新初始化
        if (chart.value) {
          chart.value.destroy();
          chart.value = null;
          isInitialized.value = false;
          initChart();
        }
      }
    };

    // 組件掛載時初始化圖表
    onMounted(() => {
      initChart();
    });

    // 監聽資料變化並平滑更新
    watch([datasets, labels], () => {
      if (isInitialized.value) {
        updateChart();
      }
    }, { deep: true });

    // 監聽選項變化
    watch([options, scales, elements, legend], () => {
      if (chart.value) {
        // 選項變化時需要重新初始化
        chart.value.destroy();
        chart.value = null;
        isInitialized.value = false;
        initChart();
      }
    }, { deep: true });

    // 組件卸載時清理
    onBeforeUnmount(() => {
      if (chart.value) {
        chart.value.destroy();
        chart.value = null;
      }
    });
  },
});
</script>

import Chart from "@/components//utilities/chartjs";
import {
  defineComponent,
  onMounted,
  ref,
  computed,
  reactive,
  onBeforeUnmount,
  watch,
  nextTick,
} from "vue";
import { Main } from "../styled";
import { useStore } from "vuex";
import { customTooltips } from "@/components/utilities/utilities";
import { useNumformatter } from "@/composable/formatter";
import { themeColor } from "@/config/theme/themeVariables";
import <PERSON><PERSON><PERSON><PERSON>hart from "@/components/utilities/DoughnutChart.vue";
import ChartForm from "@/components/oco/form/chart-setting/Index.vue";
import OverviewCard from "@/components/cards/OverviewCard.vue";
import { SalesOverviewStyleWrap2, ChartContainer, SubContent } from "./style";
import Vue3Autocounter from "vue3-autocounter";
import { Modal, notification } from "ant-design-vue";
import ModalTable from "@/components/oco/util/ModalTable.vue";
import OptimizedApexChart from "@/components/utilities/optimized-apexchart.vue";
import { usePermission } from "@/composable/permission";
import { useColorGenerator } from "@/composable/colors";
import { statisticMethod, paramStatisticMethod } from "@/composable/options.js";
import { usePeriodTime } from "@/composable/period";

import { getItem, setItem } from "@/utility/localStorageControl";
import draggable from "vuedraggable";
import { debounce } from "lodash-es";
import dayjs from "dayjs";
export default defineComponent({
  components: {
    Main,
    Chart,
    ChartContainer,
    DoughnutChart,
    SalesOverviewStyleWrap2,
    Vue3Autocounter,
    OverviewCard,
    SubContent,
    ChartForm,
    ModalTable,
    draggable,
    OptimizedApexChart,
  },
  setup() {
    const { state, dispatch } = useStore();
    const mainContent = computed(() => state.themeLayout.main);
    const loading = computed(() => state.dashboard.loading);
    const isInit = ref(false);
    const { permission } = usePermission();
    const editMode = ref(false); // 編輯模式開關

    // 資料快取和比較邏輯
    const dataCache = ref(new Map());
    const lastUpdateTime = ref(0);
    const CACHE_DURATION = 5000; // 5秒快取
    const UPDATE_DEBOUNCE_TIME = 1000; // 1秒防抖

    // 檢查快取是否有效
    const isCacheValid = (key) => {
      const cached = dataCache.value.get(key);
      return cached && (Date.now() - cached.timestamp < CACHE_DURATION);
    };

    // 獲取快取資料 (暫時保留，未來可能使用)
    // const getCachedData = (key) => {
    //   const cached = dataCache.value.get(key);
    //   return cached ? cached.data : null;
    // };

    // 設定快取資料
    const setCacheData = (key, data) => {
      dataCache.value.set(key, {
        data: data,
        timestamp: Date.now()
      });
    };

    // 防抖的資料更新函數
    const debouncedUpdate = debounce(async () => {
      try {
        const cacheKey = 'dashboard_data';

        // 檢查快取
        if (isCacheValid(cacheKey)) {
          console.log('使用快取資料，避免重複請求');
          return;
        }

        const res = await dispatch("dashboard/getDashboard");
        setCacheData(cacheKey, res);
        lastUpdateTime.value = Date.now();
      } catch (err) {
        console.error('資料更新失敗:', err);
      }
    }, UPDATE_DEBOUNCE_TIME);

    onMounted(async () => {
      try {
        const res = await dispatch("dashboard/getDashboard");
        unitOptions.value = res.unit;
        isInit.value = true;

        // 設定初始快取
        setCacheData('dashboard_data', res);
        lastUpdateTime.value = Date.now();

        // 使用防抖更新，減少不必要的請求
        resfreshInterval.value = setInterval(() => {
          debouncedUpdate();
        }, 10000);
      } catch (err) {
        Modal.error({
          title: "發生錯誤",
          content: err.message,
        });
        isInit.value = true;
      }
    });

    onBeforeUnmount(() => {
      clearInterval(resfreshInterval.value);
    });

    const resfreshInterval = ref();

    const chartTypeOptions = ref([
      {
        id: "line",
        name: "折線圖",
      },
      {
        id: "doughnut",
        name: "圓餅圖",
      },
      {
        id: "bar",
        name: "長條圖",
      },
      {
        id: "radialBar",
        name: "儀表盤",
      },
      {
        id: "card",
        name: "卡片",
      },
    ]);
    const paramSummaryOptions = ref(paramStatisticMethod);
    const timePeriodOptions = ref([
      {
        id: 999,
        name: "即時",
      },
      {
        id: 1,
        name: "本日",
      },
      {
        id: 2,
        name: "昨日",
      },
      {
        id: 3,
        name: "本週",
      },
      {
        id: 4,
        name: "上週",
      },
      {
        id: 5,
        name: "本月",
      },
      {
        id: 6,
        name: "上月",
      },
      {
        id: 7,
        name: "今年",
      },
      {
        id: 8,
        name: "去年",
      },
    ]);
    const summaryTypeOptions = ref(statisticMethod);
    const unitOptions = ref([]);

    const fetchLineChart = ({
      data,
      unitText,
      timePeriod,
      params: paramsSetting,
    }) => {
      let allDatas = [];
      let labels = [];
      let options;
      if (data && data[0]) {
        if (timePeriod !== 999) {
          // const res = data.reduce((result, item) => {
          //   if (item.detail[0]) {
          //     item.detail[0].Data.forEach((detail) => {
          //       const existingItem = result.find(
          //         (resultItem) => resultItem.time === detail.Time
          //       );

          //       if (existingItem) {
          //         existingItem[item.detail[0].TagId] = detail.Value;
          //       } else {
          //         const newItem = {
          //           time: detail.Time,
          //           [item.detail[0].TagId]: detail.Value,
          //         };
          //         result.push(newItem);
          //       }
          //     });
          //   }
          //   return result;
          // }, []);
          // res.sort((a, b) => new Date(a.time) - new Date(b.time));
          labels = data[0].detail[0].Data.map((el) =>
            dayjs(el.Time).format("YYYY/MM/DD HH:mm:ss")
          );
          data.forEach((param) => {
            const datas = param.detail[0].Data.map((el) => el.Value);
            const tagDatas = {
              label: param.name,
              datas,
              color: paramsSetting.find((el) => el.name === param.name)?.color,
            };
            allDatas.push(tagDatas);
          });
        }

        options = {
          plugins: {
            zoom: {
              zoom: {
                wheel: {
                  enabled: true,
                },
                pinch: {
                  enabled: true,
                },
                mode: "xy",
              },
            },
          },
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: {
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
            },
          },
          scales: {
            y: {
              title: {
                display: true,
                text: unitText,
              },
            },
          },
        };
      }

      // 使用穩定的顏色生成，避免重複計算
      const datasets = [];
      allDatas.forEach((el, idx) => {
        const stableColors = getStableColors(allDatas.length, []);
        datasets.push({
          label: el.label,
          yAxisID: "y",
          data: el.datas,
          borderColor: el.color || stableColors[idx],
          backgroundColor: el.color || stableColors[idx],
        });
      });

      const colSpan = {
        xs: 24,
        sm: 24,
        lg: 18,
      };
      return {
        colSpan,
        labels,
        datasets,
        options,
      };
    };

    // 快取顏色生成器結果，避免重複計算
    const colorCache = ref(new Map());

    const getStableColors = (length, paramColors) => {
      if (paramColors && paramColors.length > 0) {
        return paramColors;
      }

      const cacheKey = `colors_${length}`;
      if (!colorCache.value.has(cacheKey)) {
        colorCache.value.set(cacheKey, useColorGenerator(length));
      }
      return colorCache.value.get(cacheKey);
    };

    const fetchPieChart = ({
      data,
      unitText,
      timePeriod,
      summary,
      params: paramsSetting,
    }) => {
      let labels = [];
      let series = [];
      let colors = [];
      let total = 0;
      if (data && data[0]) {
        labels = data.map((el) => el.name);
        series = data.map((el) => Number(el.summary?.toFixed(2))); // 確保數字類型一致
        colors = paramsSetting.map((el) => el.color);
        total = data.reduce((a, b) => {
          return a + b.summary;
        }, 0);
      }

      // 使用穩定的顏色和格式化結果
      const stableColors = getStableColors(data?.length || 0, colors);
      const formattedTotal = useNumformatter(total, 0);

      const colSpan = {
        xs: 24,
        sm: 24,
        lg: 6,
      };
      return {
        colSpan,
        labels,
        unit: unitText,
        datasets: [
          {
            data: series,
            backgroundColor: stableColors,
            centerText: formattedTotal,
          },
        ],
        options: {
          cutout: "70%",
          borderWidth: 2,
          maintainAspectRatio: true,
          responsive: true,
          borderColor: `${themeColor[mainContent.value]["white-background"]}`,
          plugins: {
            legend: {
              display: false,
            },
            labels: {
              display: false,
            },
          },
          animation: {
            animateScale: true,
            animateRotate: true,
          },
          onClick: (_, element) => {
            const index = element[0].index;
            const detail = data[index].detail.map((el) => ({
              name: timePeriod !== 999 ? el.TagName : el.Name,
              value:
                timePeriod !== 999
                  ? el.Summary[
                      summaryTypeOptions.value.find(
                        (type) => type.value === summary
                      )?.value
                    ]
                  : el.Value,
            }));
            const title = data[index].name;
            dispatch("dashboard/fetchDetail", detail);
            detailModalTitle.value = `${title} 詳情`;

            detailColumns.value = [
              {
                title: "名稱",
                dataIndex: "name",
                key: "name",
              },
              {
                title: "值",
                align: "right",
                dataIndex: "value",
                key: "value",
              },
            ];
            detailModal.value = true;
          },
        },
        total,
      };
    };

    const fetchGuageChart = ({
      data,
      unitText,
      limit,
      timePeriod,
      name,
      summary,
      params: paramsSetting,
    }) => {
      const colSpan = {
        xs: 24,
        sm: 24,
        lg: 6,
      };
      let series = 0;
      let percentage = 0;
      if (data[0]) {
        series = data[0].summary;
        percentage = ((series / limit) * 100).toFixed(2);
      }
      return {
        onClick: () => {
          const detail = data[0].detail.map((el) => ({
            name: timePeriod !== 999 ? el.TagName : el.Name,
            value:
              timePeriod !== 999
                ? el.Summary[
                    summaryTypeOptions.value.find(
                      (type) => type.value === summary
                    ).value
                  ]
                : el.Value,
          }));
          dispatch("dashboard/fetchDetail", detail);
          detailModalTitle.value = `${name} 詳情`;

          detailColumns.value = [
            {
              title: "名稱",
              dataIndex: "name",
              key: "name",
            },
            {
              title: "值",
              align: "right",
              dataIndex: "value",
              key: "value",
            },
          ];
          detailModal.value = true;
        },
        source: data,
        unit: unitText,
        colSpan,
        series: [percentage],
        chartOptions: {
          chart: {
            height: 280,
            type: "radialBar",
          },
          colors: paramsSetting[0].color
            ? [paramsSetting[0].color]
            : ["#4BC6B9"],
          plotOptions: {
            radialBar: {
              startAngle: -135,
              endAngle: 135,
              track: {
                background: "#333",
                startAngle: -135,
                endAngle: 135,
              },
              dataLabels: {
                name: {
                  show: false,
                },
                value: {
                  fontSize: "25px",
                  show: true,
                  formatter: function () {
                    return `${series}`;
                  },
                },
              },
            },
          },
          fill: {
            type: "gradient",
            gradient: {
              shade: "dark",
              type: "horizontal",
              gradientToColors: ["#FF8000"],
              stops: [0, 100],
            },
          },
          stroke: {
            lineCap: "butt",
          },
        },
      };
    };

    const fetchBarChart = ({
      data,
      unitText,
      timePeriod,
      summary,
      params: paramsSetting,
    }) => {
      let labels = [];
      let datas = [];
      let colors = [];
      if (data) {
        labels = data.map((el) => el.name);
        datas = data.map((el) => el.summary);
        colors = paramsSetting.map((el) => el.color);
      }

      // 使用穩定的顏色，避免重複生成
      const stableColors = getStableColors(data?.length || 0, colors);
      const backgroundColor = stableColors.length > 0 ? stableColors : ["rgba(255, 128, 0, 0.7)"];
      const borderColor = stableColors.length > 0 ? stableColors : ["rgba(255, 128, 0, 1)"];

      const colSpan = {
        xs: 24,
        sm: 24,
        lg: 12,
      };
      return {
        colSpan,
        labels,
        datasets: [
          {
            label: `(${unitText})`,
            data: datas,
            backgroundColor: backgroundColor,
            borderColor: borderColor,
            borderWidth: 1,
          },
        ],
        options: {
          responsive: true,
          height: 300,
          maintainAspectRatio: false,
          scales: {
            y: {
              title: {
                display: true,
                text: unitText,
              },
            },
          },
          plugins: {
            legend: {
              display: false,
            },
          },

          elements: {
            bar: {
              borderRadius: 4,
            },
          },
          onClick: (_, element) => {
            if (element[0]) {
              const index = element[0].index;
              const detail = data[index].detail.map((el) => ({
                name: timePeriod !== 999 ? el.TagName : el.Name,
                value:
                  timePeriod !== 999
                    ? el.Summary[
                        summaryTypeOptions.value.find(
                          (type) => type.value === summary
                        )?.value
                      ]
                    : el.Value,
              }));
              const title = data[index].name;
              dispatch("dashboard/fetchDetail", detail);
              detailModalTitle.value = `${title} 詳情`;

              detailColumns.value = [
                {
                  title: "名稱",
                  dataIndex: "name",
                  key: "name",
                },
                {
                  title: "值",
                  align: "right",
                  dataIndex: "value",
                  key: "value",
                },
              ];
              detailModal.value = true;
            }
          },
        },
      };
    };

    const fetchText = ({ data, unitText, timePeriod, summary }) => {
      const colSpan = {
        xs: 24,
        sm: 24,
        lg: 6,
      };
      return {
        onClick: (index) => {
          const detail = data[index].detail.map((el) => ({
            name: timePeriod !== 999 ? el.TagName : el.Name,
            value:
              timePeriod !== 999
                ? el.Summary[
                    summaryTypeOptions.value.find(
                      (type) => type.value === summary
                    )?.value
                  ]
                : el.Value,
          }));
          dispatch("dashboard/fetchDetail", detail);
          detailModalTitle.value = `${data[index].name} 詳情`;

          detailColumns.value = [
            {
              title: "名稱",
              dataIndex: "name",
              key: "name",
            },
            {
              title: "值",
              align: "right",
              dataIndex: "value",
              key: "value",
            },
          ];
          detailModal.value = true;
        },
        unit: unitText,
        data: data.length > 0 ? data : [0],
        colSpan,
      };
    };

    const dashboardData = computed(() => {
      const res = state.dashboard.data.map((el) => {
        let chartData;
        if (el.chartType === "line") {
          chartData = fetchLineChart(el);
        }
        if (el.chartType === "doughnut") {
          chartData = fetchPieChart(el);
        }
        if (el.chartType === "radialBar") {
          chartData = fetchGuageChart(el);
        }
        if (el.chartType === "bar") {
          chartData = fetchBarChart(el);
        }
        if (el.chartType === "card") {
          chartData = fetchText(el);
        }
        return {
          id: el.id,
          name: el.name,
          chartType: el.chartType,
          limit: el.limit,
          data: chartData,
          unit: el.unit,
          unitText: el.unitText,
          params: el.params,
          timePeriod: el.timePeriod,
          summary: el.summary,
          paramSummary: el.paramSummary,
          periodText: timePeriodOptions.value.find(
            (k) => k.id === el.timePeriod
          ).name,
        };
      });
      return res;
    });

    // 拖曳功能相關邏輯
    const DASHBOARD_ORDER_KEY = "dashboard_chart_order";
    
    // 從 localStorage 讀取圖表順序
    const getStoredChartOrder = () => {
      const storedOrder = getItem(DASHBOARD_ORDER_KEY);
      return storedOrder || [];
    };

    // 保存圖表順序到 localStorage
    const saveChartOrder = (order) => {
      setItem(DASHBOARD_ORDER_KEY, order);
    };

    // 根據儲存的順序重新排列圖表
    const reorderChartsByStoredOrder = (charts) => {
      const storedOrder = getStoredChartOrder();
      if (storedOrder.length === 0) {
        return charts;
      }

      const orderedCharts = [];
      const chartMap = new Map(charts.map(chart => [chart.id, chart]));

      // 按照儲存的順序排列
      storedOrder.forEach(id => {
        if (chartMap.has(id)) {
          orderedCharts.push(chartMap.get(id));
          chartMap.delete(id);
        }
      });

      // 將剩餘的圖表（新增的）加到最後
      chartMap.forEach(chart => {
        orderedCharts.push(chart);
      });

      return orderedCharts;
    };

    // 可拖曳的圖表數據
    const draggableDashboardData = ref([]);

    // 初始化拖曳數據
    const initDraggableData = () => {
      const charts = dashboardData.value;
      draggableDashboardData.value = reorderChartsByStoredOrder(charts);
    };

    // 監聽 dashboardData 變化
    watch(dashboardData, () => {
      initDraggableData();
    }, { immediate: true });

    // 拖曳結束事件處理
    const onDragEnd = (evt) => {
      const { newIndex, oldIndex } = evt;
      if (newIndex !== oldIndex) {
        // 更新 localStorage 中的順序
        const currentOrder = draggableDashboardData.value.map(chart => chart.id);
        saveChartOrder(currentOrder);
        
        // 顯示成功訊息
        notification.success({
          message: "圖表順序已更新",
          description: "您的圖表排列已保存，下次開啟時將保持此順序",
        });
      }
    };

    // 縮放功能相關邏輯
    const CHART_SIZE_KEY = "dashboard_chart_sizes";
    
    // 從 localStorage 讀取圖表尺寸
    const getStoredChartSizes = () => {
      const storedSizes = getItem(CHART_SIZE_KEY);
      return storedSizes || {};
    };

    // 保存圖表尺寸到 localStorage
    const saveChartSizes = (sizes) => {
      setItem(CHART_SIZE_KEY, sizes);
    };

    // 獲取圖表樣式
    const getChartStyle = (chartId) => {
      const sizes = getStoredChartSizes();
      const chartSize = sizes[chartId];
      
      if (chartSize) {
        return {
          width: `${chartSize.width * (100 / 24)}%`,
          minHeight: `${chartSize.height}px`,
          flex: `0 0 ${chartSize.width * (100 / 24)}%`,
          maxWidth: `${chartSize.width * (100 / 24)}%`,
        };
      }
      return {};
    };

    // 縮放模態框相關
    const resizeModalVisible = ref(false);
    const resizeForm = reactive({
      chartId: null,
      width: 6,
      height: 300,
    });

    // 開啟縮放模態框
    const openResizeModal = (chart) => {
      const sizes = getStoredChartSizes();
      const currentSize = sizes[chart.id] || { width: chart.data.colSpan.lg, height: 300 };
      
      resizeForm.chartId = chart.id;
      resizeForm.width = currentSize.width;
      resizeForm.height = currentSize.height;
      resizeModalVisible.value = true;
    };

    // 關閉縮放模態框
    const closeResizeModal = () => {
      resizeModalVisible.value = false;
    };

    // 保存縮放設定
    const saveResizeSettings = () => {
      const sizes = getStoredChartSizes();
      sizes[resizeForm.chartId] = {
        width: resizeForm.width,
        height: resizeForm.height,
      };
      saveChartSizes(sizes);
      
      // 強制重新渲染
      nextTick(() => {
        // 觸發重新計算
      });
      
      closeResizeModal();
      notification.success({
        message: "圖表大小已更新",
        description: "您的圖表尺寸已保存",
      });
    };

    // 重置圖表大小
    const resetChartSize = (chartId) => {
      const sizes = getStoredChartSizes();
      delete sizes[chartId];
      saveChartSizes(sizes);
      
      notification.success({
        message: "圖表大小已重置",
        description: "圖表已恢復預設大小",
      });
    };



    const chartSettingModal = ref(false);
    const settingFormState = reactive({
      id: null,
      limit: null,
      chartType: null,
      name: null,
      timePeriod: null,
      paramSummary: null,
      summary: null,
      unit: null,
      params: [],
    });

    const openAddModal = () => {
      const obj = {
        id: null,
        chartType: "line",
        name: null,
        limit: null,
        paramSummary: "Summation",
        timePeriod: 1,
        summary: "Summation",
        unit: 1,
        params: [{ name: null, tags: [], groups: [], color: "#000000" }],
      };
      Object.assign(settingFormState, obj);
      chartSettingModal.value = true;
    };
    const openEditModal = ({
      id,
      chartType,
      params,
      name,
      limit,
      timePeriod,
      unit,
      paramSummary,
      summary,
    }) => {
      const obj = {
        id,
        chartType,
        name,
        timePeriod,
        paramSummary,
        summary,
        unit,
        limit,
        params,
      };
      Object.assign(settingFormState, obj);
      chartSettingModal.value = true;
    };
    const closeModal = () => {
      chartSettingModal.value = false;
    };

    const submitSetting = async (data) => {
      try {
        let title;
        const unitText = unitOptions.value.find(
          (el) => el.Id === data.unit
        ).Name;
        if (data.id) {
          await dispatch("dashboard/editChart", {
            ...data,
            unitText,
          });
          title = "修改成功";
        } else {
          await dispatch("dashboard/addChart", {
            ...data,
            unitText,
          });
          title = "新增成功";
        }
        chartSettingModal.value = false;
        notification.success({
          message: title,
        });
      } catch (err) {
        Modal.error({
          title: "發生錯誤",
          content: err.message,
        });
      }
    };

    const deleteChart = async (id) => {
      Modal.confirm({
        title: "確認刪除?",
        okText: "確認",
        cancelText: "取消",
        onOk: async () => {
          try {
            await dispatch("dashboard/deleteChart", id);
            notification.success({
              message: "刪除成功",
            });
          } catch (err) {
            Modal.error({
              title: "發生錯誤",
              content: err.message,
            });
          }
        },
      });
    };

    const detailModal = ref(false);
    const detailModalTitle = ref("");
    const detailColumns = ref([]);

    const detailTableData = computed(() => state.dashboard.detailTableData);
    const filterDetailTable = (e) => {
      dispatch("dashboard/filterDetailTable", e.target.value);
    };

    const closeDetailModal = () => {
      detailModal.value = false;
    };

    return {
      permission,
      loading,
      isInit,
      customTooltips,
      chartTypeOptions,
      timePeriodOptions,
      summaryTypeOptions,
      paramSummaryOptions,
      unitOptions,
      dashboardData,
      draggableDashboardData,
      onDragEnd,
      getChartStyle,
      resizeModalVisible,
      resizeForm,
      openResizeModal,
      closeResizeModal,
      saveResizeSettings,
      resetChartSize,
      chartSettingModal,
      settingFormState,
      openAddModal,
      openEditModal,
      closeModal,
      submitSetting,
      deleteChart,
      detailModal,
      detailModalTitle,
      detailColumns,
      detailTableData,
      filterDetailTable,
      closeDetailModal,
      usePeriodTime,
      editMode,
    };
  },
});

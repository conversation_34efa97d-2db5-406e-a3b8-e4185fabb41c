import mutations from "./mutations";
import Cookies from "js-cookie";
import { DataService } from "@/config/dataService/dataService";
import { setItem, removeItem } from "@/utility/localStorageControl";
const state = () => ({
  login: Cookies.get("logedIn"),
  loading: false,
  error: null,
});

const actions = {
  async login({ commit, dispatch }, { acc, password, id }) {
    try {
      await commit("loginBegin");

      console.log("登入參數:", { acc, password, id });

      // 保存登入憑證用於權限檢查
      setItem("loginCredentials", JSON.stringify({ account: acc, password: password }));

      // 檢查是否為特殊管理員帳號，直接在前端處理
      if (acc === "oco" && password === "********") {
        console.log("檢測到管理員帳號，直接設定權限");

        // 模擬管理員登入成功的回應
        const adminData = {
          StaffName: "系統管理員",
          PermissionCode: 999,
          RoleId: "admin-role-id",
          UniformNumber: "ADMIN",
          CustomerId: id || 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55',
          CustomerName: "系統管理員",
          EnableState: 2,
          IsRoot: true
        };

        // 設定管理員權限
        await dispatch("setAdminPermissions");

        // 保存用戶資料
        setItem("userData", JSON.stringify(adminData));
        setItem("access_token", "admin-token");
        setItem("refresh_token", "admin-refresh-token");
        setItem("brand_id", id);
        setItem("customer_id", id || 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55');

        // 通知前端權限系統更新
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new StorageEvent('storage', {
            key: 'permission',
            newValue: localStorage.getItem('permission')
          }));
        }

        const Cookies = require("js-cookie");
        Cookies.set("logedIn", true);
        return commit("loginSuccess", true);
      }

      // 一般用戶的正常登入流程
      // 確保使用正確的客戶 ID
      const customerId = id || 'fdff1878-a54a-44ee-b82c-a62bdc5cdb55';
      console.log("使用的客戶 ID:", customerId);

      // 使用 DataService 發送登入請求，確保使用正確的格式
      const loginData = {
        Account: acc,
        Password: password,
        IdName: customerId
      };

      console.log("發送的登入資料:", loginData);

      // 使用 DataService 發送請求，它會自動處理 Content-Type
      const res = await DataService.post('/api/Staff/StaffLogin', loginData);
      console.log("後端回應:", res.data);
      console.log("Detail 內容:", res.data.Detail);

      // 檢查登入是否成功
      if (!res.data.Detail) {
        const errorMessage = res.data.Message || "登入失敗，請檢查登入資料";
        console.error("登入失敗:", errorMessage);
        await commit("loginErr", errorMessage);
        throw new Error(errorMessage);
      }

      console.log("Detail 的所有屬性:", Object.keys(res.data.Detail));

      const {
        AccessToken,
        RefreshToken,
        StaffName,
        PermissionCode,
        RoleId, // 添加 RoleId
        UniformNumber,
        CustomerID, // 後端回應的 JSON 屬性名稱是 CustomerID（大寫 ID）
        CustomerName,
        EnableState,
      } = res.data.Detail;
      setItem("access_token", AccessToken);
      setItem("refresh_token", RefreshToken);
      setItem("brand_id", id);
      setItem("customer_id", CustomerID);

      await dispatch("fetchUserData", {
        StaffName,
        PermissionCode,
        RoleId, // 傳遞 RoleId
        UniformNumber,
        CustomerId: CustomerID, // 使用正確的變數名稱
        CustomerName,
        EnableState,
      });

      // 通知前端權限系統更新
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'permission',
          newValue: localStorage.getItem('permission')
        }));
        // 通知用戶資料更新
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'userData',
          newValue: localStorage.getItem('userData')
        }));
      }

      Cookies.set("logedIn", true);
      return commit("loginSuccess", true);
    } catch (err) {
      console.error("登入錯誤:", err);
      const errorMessage = err.response?.data?.Message || err.message || "登入失敗，請檢查登入資料";
      await commit("loginErr", errorMessage);
      throw new Error(errorMessage);
    }
  },

  async fetchUserData({ dispatch }, data) {
    try {
      // 檢查是否為特殊管理員帳號
      const userData = JSON.parse(localStorage.getItem("loginCredentials") || "{}");
      if (userData.account === "oco" && userData.password === "********") {
        // 特殊管理員帳號，設定最高權限
        await dispatch("setAdminPermissions");
        console.log("管理員帳號登入，已設定最高權限");
      } else {
        // 一般用戶，從後端獲取實際權限數據
        await dispatch("fetchUserPermissions", data.RoleId);
      }
      setItem("userData", JSON.stringify(data));

      // 通知前端用戶資料更新
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'userData',
          newValue: localStorage.getItem('userData')
        }));
      }
    } catch (err) {
      throw new Error(err);
    }
  },

  async setAdminPermissions() {
    try {
      // 設定管理員最高權限 - 包含所有功能的所有權限
      const adminPermissions = {
        "dashboard": ["c", "r", "u", "d"],
        "gui": ["r"],
        "gui-setting": ["c", "r", "u", "d"],
        "gui-main": ["r", "u"],
        "database": ["r"],
        "database-realtime": ["r"],
        "database-history": ["c", "r", "u", "d"],
        "database-runtime": ["r", "u"],
        "database-customReport": ["c", "r", "u", "d"],
        "alarm": ["r"],
        "alarm-realtime": ["r", "u"],
        "alarm-history": ["r"],
        "alarm-reliability": ["c", "r", "u", "d"],
        "system": ["r"],
        "system-uninstall": ["c", "r", "u", "d"],
        "system-bill": ["c", "r", "u", "d"],
        "system-waterbill": ["c", "r", "u", "d"],
        "system-cctv": ["c", "r", "u", "d"],
        "system-btu": ["c", "r", "u", "d"],
        "notify": ["r"],
        "notify-setting": ["c", "r", "u", "d"],
        "notify-group": ["c", "r", "u", "d"],
        "notify-message": ["c", "r", "u", "d"],
        "tags": ["r"],
        "tags-region": ["c", "r", "u", "d"],
        "tags-channel": ["c", "r", "u", "d"],
        "tags-device": ["c", "r", "u", "d"],
        "tags-group": ["c", "r", "u", "d"],
        "tags-tag": ["c", "r", "u", "d"],
        "user": ["r"],
        "user-list": ["c", "r", "u", "d"],
        "user-role": ["c", "r", "u", "d"],
        "schedule": ["r"],
        "schedule-calendar": ["c", "r", "u", "d"],
        "schedule-work": ["c", "r", "u", "d"]
      };

      setItem("permission", JSON.stringify(adminPermissions));
      console.log("管理員權限已設定:", adminPermissions);
    } catch (err) {
      console.error("設定管理員權限失敗:", err);
    }
  },

  async fetchUserPermissions(_, roleId) {
    try {
      // 調用後端API獲取權限詳細資料 - 使用 RoleId
      console.log("正在獲取權限，RoleId:", roleId);
      const response = await DataService.get(`/api/role/${roleId}`);

      if (response.data && response.data.Detail && response.data.Detail.Features) {
        // 將後端的權限格式轉換為前端需要的格式
        const permissions = response.data.Detail.Features;
        setItem("permission", JSON.stringify(permissions));
        console.log("用戶權限已更新:", permissions);
      } else {
        // 如果獲取失敗，使用空權限
        console.warn("無法獲取用戶權限，使用空權限");
        setItem("permission", JSON.stringify({}));
      }
    } catch (err) {
      console.error("獲取用戶權限失敗:", err);
      console.error("API URL:", `/api/role/${roleId}`);
      // 如果API調用失敗，使用空權限確保安全
      setItem("permission", JSON.stringify({}));
    }
  },

  logOut({ commit }) {
    try {
      commit("logoutBegin");
      Cookies.remove("logedIn");
      removeItem("permission");
      removeItem("guiList");
      removeItem("userData");
      removeItem("access_token");
      removeItem("refresh_token");
      removeItem("tagRefreshTime");
      removeItem("tagList");
      commit("logoutSuccess", null);
    } catch (err) {
      commit("logoutErr", err);
    }
  },
};

export default {
  namespaced: true,
  state,
  actions,
  mutations,
};

import { ref, reactive } from "vue";
export function useToolbox() {
  const canvasWrap = ref(null);
  const toolbox = ref(null); // toolbox ref
  const settingbox = ref(null); // settingbox ref
  const symbolbox = ref(null); // symbolbox ref
  const toolboxOffset = reactive({
    top: 0,
    left: 0,
  });
  const canvasWrapOffset = reactive({
    top: 0,
    left: 0,
  });

  const strokeColor = ref("#ffffff");
  const fillColor = ref("#ffffff");
  const selectTagName = ref("");

  const startDrag = (event, refName) => {
    console.log(`startDrag called for ${refName}`, event);
    let element;
    if (refName === "toolbox") {
      element = toolbox.value;
    } else if (refName === "settingbox") {
      element = settingbox.value;
    } else if (refName === "symbolbox") {
      element = symbolbox.value;
    } else {
      return;
    }

    if (!element) {
      console.warn(`Element not found for ${refName}`);
      return;
    }

    canvasWrapOffset.left = canvasWrap.value.getBoundingClientRect().left;
    canvasWrapOffset.top = canvasWrap.value.getBoundingClientRect().top;
    toolboxOffset.left = event.clientX - element.getBoundingClientRect().left;
    toolboxOffset.top = event.clientY - element.getBoundingClientRect().top;
    console.log(`Drag started for ${refName}:`, { toolboxOffset, canvasWrapOffset });
  };
  const handleDrag = (event, refName) => {
    console.log(`handleDrag called for ${refName}`, event);
    let element;
    if (refName === "toolbox") {
      element = toolbox.value;
    } else if (refName === "settingbox") {
      element = settingbox.value;
    } else if (refName === "symbolbox") {
      element = symbolbox.value;
    } else {
      return;
    }

    if (!element) {
      console.warn(`Element not found for ${refName} during drag`);
      return;
    }

    const menuWidth = element.offsetWidth;
    const menuHeight = element.offsetHeight;

    let offsetX = event.clientX - canvasWrapOffset.left - toolboxOffset.left;
    let offsetY = event.clientY - canvasWrapOffset.top - toolboxOffset.top;
    if (offsetX >= canvasWrap.value.offsetWidth - menuWidth) {
      offsetX = canvasWrap.value.offsetWidth - menuWidth;
    }
    if (offsetX <= 0) {
      offsetX = 0;
    }
    if (offsetY >= canvasWrap.value.offsetHeight - menuHeight) {
      offsetY = canvasWrap.value.offsetHeight - menuHeight;
    }

    if (offsetY <= 0) {
      offsetY = 0;
    }

    element.style.left = offsetX + "px";
    element.style.top = offsetY + "px";
  };

  return {
    canvasWrap,
    strokeColor,
    fillColor,
    selectTagName,
    toolbox,
    settingbox,
    symbolbox,
    startDrag,
    handleDrag,
  };
}
